<script lang="ts">
  import StatusBadge from './StatusBadge.svelte';
  import type { SyncStatus } from './types';

  export let label: string;
  export let status: SyncStatus;
  export let value: any;
  export let isTagsList: boolean = false;

  $: displayValue = formatValue(value, isTagsList);

  function formatValue(val: any, isTags: boolean): string {
    if (val === null || val === undefined) {
      return 'None';
    }

    if (typeof val === 'boolean') {
      return val ? 'Yes' : 'No';
    }

    if (isTags && Array.isArray(val)) {
      if (val.length === 0) {
        return 'None';
      }
      return val.map(tag => tag.name || tag).join(', ');
    }

    if (typeof val === 'object' && val.name) {
      return val.name;
    }

    return String(val);
  }

  function getTagsArray(val: any): string[] {
    if (!val || !Array.isArray(val)) {
      return [];
    }
    return val.map(tag => tag.name || tag).filter(Boolean);
  }
</script>

<div class="ghost-property-section">
  <div class="ghost-property-header">
    <span class="ghost-property-label">{label}</span>
    <StatusBadge {status} compact={true} />
  </div>

  {#if isTagsList && value && Array.isArray(value) && value.length > 0}
    <div class="ghost-tags-container">
      {#each getTagsArray(value) as tag}
        <span class="ghost-tag-pill">{tag}</span>
      {/each}
    </div>
  {:else}
    <div class="ghost-property-field">
      {displayValue}
    </div>
  {/if}
</div>

<style>
  .ghost-property-section {
    margin-bottom: 20px;
  }

  .ghost-property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .ghost-property-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-normal);
  }

  .ghost-property-field {
    background: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-normal);
    min-height: 20px;
    display: flex;
    align-items: center;
  }

  .ghost-tags-container {
    background: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    padding: 8px 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    min-height: 36px;
    align-items: center;
  }

  .ghost-tag-pill {
    background: var(--background-modifier-border);
    color: var(--text-normal);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
  }
</style>

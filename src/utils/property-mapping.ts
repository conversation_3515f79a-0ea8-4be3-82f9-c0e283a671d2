/**
 * Centralized property mapping system for Ghost ↔ Obsidian synchronization
 *
 * This module defines the canonical mapping between Ghost API properties and
 * Obsidian frontmatter properties, ensuring consistency across all sync operations.
 */

export interface PropertyMapping {
  /** Ghost API property name (lowercase, snake_case) */
  ghostProperty: string;
  /** Obsidian frontmatter property name (Title Case) */
  obsidianProperty: string;
  /** Alternative Obsidian property name (lowercase) for backward compatibility */
  obsidianAlternative?: string;
  /** Whether this property should be included in sync status comparison */
  includeInSync: boolean;
  /** Whether this property should be displayed in the UI */
  displayInUI: boolean;
  /** Custom comparison function for complex properties */
  customComparison?: (ghostValue: any, obsidianValue: any) => 'synced' | 'different' | 'unknown';
  /** Custom value extraction function for Ghost values */
  extractGhostValue?: (ghostPost: any) => any;
  /** Custom value extraction function for Obsidian values */
  extractObsidianValue?: (frontmatter: any, file?: any, syncMetadata?: any) => any;
}

/**
 * Canonical property mappings between Ghost and Obsidian
 * Order matters for display purposes
 */
export const PROPERTY_MAPPINGS: PropertyMapping[] = [
  {
    ghostProperty: 'title',
    obsidianProperty: 'Title',
    obsidianAlternative: 'title',
    includeInSync: true,
    displayInUI: true
  },
  {
    ghostProperty: 'slug',
    obsidianProperty: 'Slug',
    obsidianAlternative: 'slug',
    includeInSync: true,
    displayInUI: true
  },
  {
    ghostProperty: 'status',
    obsidianProperty: 'Status',
    obsidianAlternative: 'status',
    includeInSync: true,
    displayInUI: true
  },
  {
    ghostProperty: 'tags',
    obsidianProperty: 'Tags',
    obsidianAlternative: 'tags',
    includeInSync: true,
    displayInUI: true,
    customComparison: (ghostValue: any[], obsidianValue: any[]) => {
      if (!ghostValue && !obsidianValue) return 'synced';
      if (!ghostValue || !obsidianValue) return 'unknown';

      const ghostTagNames = ghostValue.map(tag => typeof tag === 'string' ? tag : tag.name).sort();
      const obsidianTagNames = Array.isArray(obsidianValue) ? obsidianValue.slice().sort() : [];

      if (ghostTagNames.length !== obsidianTagNames.length) return 'different';

      for (let i = 0; i < ghostTagNames.length; i++) {
        if (ghostTagNames[i] !== obsidianTagNames[i]) return 'different';
      }

      return 'synced';
    },
    extractGhostValue: (ghostPost: any) => ghostPost.tags?.map((tag: any) => tag.name) || [],
    extractObsidianValue: (frontmatter: any) => {
      const tags = frontmatter.Tags || frontmatter.tags;
      return Array.isArray(tags) ? tags : [];
    }
  },
  {
    ghostProperty: 'primary_tag',
    obsidianProperty: 'Primary Tag',
    obsidianAlternative: 'primary_tag',
    includeInSync: true,
    displayInUI: false, // Primary Tag should NOT be in frontmatter, only stored in metadata and displayed in tab
    extractGhostValue: (ghostPost: any) => {
      const primaryTag = ghostPost.primary_tag?.name;
      return (typeof primaryTag === 'string' && primaryTag.trim().length > 0) ? primaryTag.trim() : null;
    },
    extractObsidianValue: (frontmatter: any, file?: any, syncMetadata?: any) => {
      // Primary Tag is stored in sync metadata, not frontmatter
      if (file && syncMetadata) {
        return syncMetadata.getStoredPrimaryTag(file);
      }

      // Fallback to frontmatter for backward compatibility (shouldn't happen normally)
      const primaryTag = frontmatter['Primary Tag'] || frontmatter.primary_tag;
      return (typeof primaryTag === 'string' && primaryTag.trim().length > 0) ? primaryTag.trim() : null;
    }
  },
  {
    ghostProperty: 'visibility',
    obsidianProperty: 'Visibility',
    obsidianAlternative: 'visibility',
    includeInSync: true,
    displayInUI: true,
    extractGhostValue: (ghostPost: any) => {
      // Ghost defaults to 'public' if visibility is null/undefined
      return ghostPost.visibility || 'public';
    },
    extractObsidianValue: (frontmatter: any) => {
      // Obsidian defaults to 'public' if visibility is not specified
      const visibility = frontmatter['Visibility'] || frontmatter.visibility;
      return visibility || 'public';
    }
  },
  {
    ghostProperty: 'featured',
    obsidianProperty: 'Featured',
    obsidianAlternative: 'featured',
    includeInSync: true,
    displayInUI: true,
    customComparison: (ghostValue: any, obsidianValue: any) => {
      // Handle boolean values properly
      const ghostBool = Boolean(ghostValue);
      const obsidianBool = Boolean(obsidianValue);
      return ghostBool === obsidianBool ? 'synced' : 'different';
    }
  },
  {
    ghostProperty: 'feature_image',
    obsidianProperty: 'Featured Image',
    obsidianAlternative: 'feature_image',
    includeInSync: true,
    displayInUI: false, // Featured image is displayed as preview, not as property
    extractObsidianValue: (frontmatter: any) => frontmatter['Featured Image'] || frontmatter.feature_image || null
  },
  {
    ghostProperty: 'newsletter',
    obsidianProperty: 'Newsletter',
    obsidianAlternative: 'newsletter',
    includeInSync: true,
    displayInUI: true,
    extractGhostValue: (ghostPost: any) => {
      // Defensive programming: ensure we handle all possible newsletter structures
      if (!ghostPost) return null;
      if (ghostPost.newsletter === null || ghostPost.newsletter === undefined) return null;
      if (typeof ghostPost.newsletter === 'string') return ghostPost.newsletter;
      if (typeof ghostPost.newsletter === 'object' && ghostPost.newsletter.name) {
        return ghostPost.newsletter.name;
      }
      return null;
    },
    extractObsidianValue: (frontmatter: any) => {
      if (!frontmatter) return null;
      const value = frontmatter.Newsletter || frontmatter.newsletter;
      if (value === null || value === undefined) return null;
      if (typeof value === 'string') return value.trim() || null;
      return null;
    },
    customComparison: (_ghostValue: any, _obsidianValue: any) => {
      // Newsletter and email properties are read-only and derived from Ghost
      // If we have a Ghost post, these should always be considered synced
      // since they represent the current state in Ghost
      return 'synced';
    }
  },
  {
    ghostProperty: 'email_sent',
    obsidianProperty: 'Email Sent',
    obsidianAlternative: 'email_sent',
    includeInSync: true,
    displayInUI: true,
    extractGhostValue: (ghostPost: any) => {
      // Defensive programming: ensure we handle all possible email structures
      if (!ghostPost) return 'No';
      if (ghostPost.email === null || ghostPost.email === undefined) return 'No';
      if (typeof ghostPost.email === 'boolean') return ghostPost.email ? 'Yes' : 'No';
      if (typeof ghostPost.email === 'object' && ghostPost.email.id) return 'Yes';
      return 'No';
    },
    extractObsidianValue: (frontmatter: any) => {
      if (!frontmatter) return 'No';
      const value = frontmatter['Email Sent'] || frontmatter.email_sent;
      if (value === true || value === 'Yes' || value === 'yes' || value === 'YES') return 'Yes';
      if (value === false || value === 'No' || value === 'no' || value === 'NO') return 'No';
      return 'No'; // Default to No
    },
    customComparison: (_ghostValue: any, _obsidianValue: any) => {
      // Newsletter and email properties are read-only and derived from Ghost
      // If we have a Ghost post, these should always be considered synced
      // since they represent the current state in Ghost
      return 'synced';
    }
  },
  {
    ghostProperty: 'created_at',
    obsidianProperty: 'Created At',
    obsidianAlternative: 'created_at',
    includeInSync: false, // Don't sync creation dates
    displayInUI: false
  },
  {
    ghostProperty: 'updated_at',
    obsidianProperty: 'Updated At',
    obsidianAlternative: 'updated_at',
    includeInSync: false, // Don't sync update dates
    displayInUI: false
  },
  {
    ghostProperty: 'published_at',
    obsidianProperty: 'Published At',
    obsidianAlternative: 'published_at',
    includeInSync: false, // Don't sync publish dates
    displayInUI: false
  }
];

/**
 * Property mapping utilities
 */
export class PropertyMapper {
  /**
   * Get property mapping by Ghost property name
   */
  static getByGhostProperty(ghostProperty: string): PropertyMapping | undefined {
    return PROPERTY_MAPPINGS.find(mapping => mapping.ghostProperty === ghostProperty);
  }

  /**
   * Get property mapping by Obsidian property name (handles both Title Case and lowercase)
   */
  static getByObsidianProperty(obsidianProperty: string): PropertyMapping | undefined {
    return PROPERTY_MAPPINGS.find(mapping =>
      mapping.obsidianProperty === obsidianProperty ||
      mapping.obsidianAlternative === obsidianProperty
    );
  }

  /**
   * Get all properties that should be included in sync status
   */
  static getSyncProperties(): PropertyMapping[] {
    return PROPERTY_MAPPINGS.filter(mapping => mapping.includeInSync);
  }

  /**
   * Get all properties that should be displayed in UI
   */
  static getDisplayProperties(): PropertyMapping[] {
    return PROPERTY_MAPPINGS.filter(mapping => mapping.displayInUI);
  }

  /**
   * Extract value from Ghost post using property mapping
   */
  static extractGhostValue(ghostPost: any, mapping: PropertyMapping): any {
    if (mapping.extractGhostValue) {
      return mapping.extractGhostValue(ghostPost);
    }
    return ghostPost[mapping.ghostProperty];
  }

  /**
   * Extract value from Obsidian frontmatter using property mapping
   */
  static extractObsidianValue(frontmatter: any, mapping: PropertyMapping, file?: any, syncMetadata?: any): any {
    if (mapping.extractObsidianValue) {
      return mapping.extractObsidianValue(frontmatter, file, syncMetadata);
    }

    // Try Title Case first, then lowercase
    const titleCaseValue = frontmatter[mapping.obsidianProperty];
    if (titleCaseValue !== undefined) {
      return titleCaseValue;
    }

    if (mapping.obsidianAlternative) {
      return frontmatter[mapping.obsidianAlternative];
    }

    return undefined;
  }

  /**
   * Compare values using property mapping
   */
  static compareValues(ghostPost: any, frontmatter: any, mapping: PropertyMapping, file?: any, syncMetadata?: any): 'synced' | 'different' | 'unknown' {
    const ghostValue = this.extractGhostValue(ghostPost, mapping);
    const obsidianValue = this.extractObsidianValue(frontmatter, mapping, file, syncMetadata);

    if (mapping.customComparison) {
      return mapping.customComparison(ghostValue, obsidianValue);
    }

    // Default comparison
    if (ghostValue === undefined && obsidianValue === undefined) return 'synced';
    if (ghostValue === null && obsidianValue === undefined) return 'synced';
    if (ghostValue === undefined && obsidianValue === null) return 'synced';
    if (ghostValue === null && obsidianValue === null) return 'synced';

    // Handle date comparison
    if (typeof ghostValue === 'string' && typeof obsidianValue === 'string') {
      const ghostDate = new Date(ghostValue);
      const obsidianDate = new Date(obsidianValue);
      if (!isNaN(ghostDate.getTime()) && !isNaN(obsidianDate.getTime())) {
        return Math.abs(ghostDate.getTime() - obsidianDate.getTime()) < 1000 ? 'synced' : 'different';
      }
    }

    return ghostValue === obsidianValue ? 'synced' : 'different';
  }

  /**
   * Create a normalized frontmatter object with proper property names
   * Only includes properties that should be displayed in UI
   */
  static normalizeToObsidian(frontmatter: any): any {
    const normalized: any = {};

    // Only apply property mappings for properties that should be displayed in UI
    for (const mapping of PROPERTY_MAPPINGS) {
      if (!mapping.displayInUI) {
        continue; // Skip properties that shouldn't be displayed
      }

      const value = this.extractObsidianValue(frontmatter, mapping);
      if (value !== undefined) {
        // Set the canonical Title Case property
        normalized[mapping.obsidianProperty] = value;
      }
    }

    // Copy any non-mapped properties that don't conflict with our mappings
    for (const [key, value] of Object.entries(frontmatter)) {
      // Skip if this property is handled by a mapping
      const mapping = this.getByObsidianProperty(key) || this.getByGhostProperty(key);
      if (mapping) {
        continue; // Already handled above
      }

      // Include non-mapped properties
      normalized[key] = value;
    }

    return normalized;
  }



  /**
   * Create a normalized object with Ghost property names
   */
  static normalizeToGhost(frontmatter: any): any {
    const normalized: any = {};

    // First, copy all properties that don't have mappings
    const mappedObsidianProperties = new Set();
    for (const mapping of PROPERTY_MAPPINGS) {
      mappedObsidianProperties.add(mapping.obsidianProperty);
      if (mapping.obsidianAlternative) {
        mappedObsidianProperties.add(mapping.obsidianAlternative);
      }
    }

    // Copy unmapped properties
    for (const [key, value] of Object.entries(frontmatter)) {
      if (!mappedObsidianProperties.has(key)) {
        normalized[key] = value;
      }
    }

    // Then apply property mappings
    for (const mapping of PROPERTY_MAPPINGS) {
      const value = this.extractObsidianValue(frontmatter, mapping);
      if (value !== undefined) {
        normalized[mapping.ghostProperty] = value;
      }
    }

    return normalized;
  }

  /**
   * Extract and validate Ghost post properties from frontmatter
   * Handles normalization, validation, and special logic for Ghost post creation
   */
  static ghostPropertiesFromFrontMatter(frontmatter: any, options: { isUpdate?: boolean, existingPost?: any } = {}): any {
    const { isUpdate = false, existingPost = null } = options;

    // First normalize the frontmatter to Ghost property names
    const normalized = this.normalizeToGhost(frontmatter);

    const ghostProperties: any = {};

    // Basic properties
    if (normalized.title) {
      ghostProperties.title = normalized.title;
    }

    // Slug - generate from title if not provided
    if (normalized.slug) {
      ghostProperties.slug = normalized.slug;
    } else if (normalized.title) {
      ghostProperties.slug = this.slugify(normalized.title);
    }

    // Status
    ghostProperties.status = normalized.status || 'draft';

    // Visibility
    ghostProperties.visibility = normalized.visibility || 'public';

    // Featured flag
    ghostProperties.featured = Boolean(normalized.featured);

    // Handle tags with primary tag support
    if (normalized.tags && Array.isArray(normalized.tags)) {
      let tags = [...normalized.tags];

      // If primary tag is specified, ensure it's first in the array
      const primaryTag = this.validateAndNormalizePrimaryTag(normalized.primary_tag);
      if (primaryTag) {
        // Remove all instances of primary tag from current positions (case-insensitive)
        tags = tags.filter(tag =>
          typeof tag === 'string' && tag.toLowerCase() !== primaryTag.toLowerCase()
        );
        // Add primary tag at the beginning
        tags.unshift(primaryTag);
      }

      ghostProperties.tags = tags;
    } else if (normalized.primary_tag) {
      // Handle case where primary tag is specified but no tags array exists
      const primaryTag = this.validateAndNormalizePrimaryTag(normalized.primary_tag);
      if (primaryTag) {
        ghostProperties.tags = [primaryTag];
      }
    }

    // Handle newsletter
    if (normalized.newsletter && typeof normalized.newsletter === 'string') {
      // Note: We store the newsletter name in frontmatter, but Ghost API expects newsletter ID
      // The actual newsletter ID resolution will be handled in the API layer
      ghostProperties.newsletter_name = normalized.newsletter;
    }

    // Handle feature_image carefully - preserve existing if not specified in frontmatter
    // Note: feature_image is now read-only in frontmatter and handled via Ghost Tab
    const frontmatterFeatureImage = normalized.feature_image || normalized.image;
    if (frontmatterFeatureImage) {
      // Explicitly set feature image from frontmatter (for backward compatibility)
      ghostProperties.feature_image = frontmatterFeatureImage;
    } else if (isUpdate && existingPost && existingPost.feature_image) {
      // Preserve existing feature image for updates when not specified in frontmatter
      ghostProperties.feature_image = existingPost.feature_image;
    } else {
      // Only set to null for new posts or when explicitly removing
      ghostProperties.feature_image = null;
    }

    return ghostProperties;
  }

  /**
   * Validates and normalizes a primary tag value
   * @param primaryTag - The primary tag value to validate
   * @returns The normalized primary tag or null if invalid
   */
  static validateAndNormalizePrimaryTag(primaryTag: any): string | null {
    // Check if primary tag is a valid string
    if (typeof primaryTag !== 'string') {
      return null;
    }

    // Trim whitespace
    const trimmed = primaryTag.trim();

    // Check if empty after trimming
    if (trimmed.length === 0) {
      return null;
    }

    // Check reasonable length limits (Ghost has a 191 character limit for tag names)
    if (trimmed.length > 191) {
      console.warn(`Primary tag "${trimmed}" exceeds maximum length of 191 characters, truncating`);
      return trimmed.substring(0, 191).trim();
    }

    return trimmed;
  }

  /**
   * Create a slug from text
   */
  static slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
}

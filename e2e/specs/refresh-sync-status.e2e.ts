import {
  setupE2ETestHooks,
  executeCommand,
  expectNotice,
  expectPostFile
} from '../helpers/shared-context';
import { completeModalInteraction } from '../helpers/modal-helpers';
import {
  openGhostTab,
  clickSyncButton,
  waitForGhostTabStatus,
  syncPost
} from '../helpers/ghost-tab-helpers';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';

import { test, expect } from 'vitest';

// Setup screenshot capture on test failures
setupTestFailureHandler();

describe("Ghost Tab: Refresh Sync Status", () => {
  const context = setupE2ETestHooks();

  test("should show refresh button in Ghost tab header", async () => {
    const testTitle = "Test Post for Refresh";

    // Create a new post
    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );
    await expectNotice(context, "Created new post");

    // Sync the post to Ghost first
    await syncPost(context);

    // Open Ghost tab
    await openGhostTab(context);

    // Check that refresh button is visible in the header
    await context.page.waitForSelector('.ghost-sync-refresh-btn', { timeout: 5000 });

    // Check that the button has the correct title attribute
    const titleAttr = await context.page.getAttribute('.ghost-sync-refresh-btn', 'title');
    expect(titleAttr).toBe('Refresh sync status from Ghost');
  });

  test("should refresh sync status when refresh button is clicked", async () => {
    const testTitle = "Test Post for Refresh Action";

    // Create a new post
    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );
    await expectNotice(context, "Created new post");

    // Sync the post to Ghost first
    await syncPost(context);

    // Open Ghost tab
    await openGhostTab(context);

    // Click the refresh button
    const refreshButton = context.page.locator('.ghost-sync-refresh-btn');
    await refreshButton.click();

    // Expect a notice about refreshing
    await expectNotice(context, "Refreshing sync status...", 5000);

    // Expect a notice about completion
    await expectNotice(context, "Sync status refreshed", 10000);

    // Verify that the sync status is still displayed correctly
    await waitForGhostTabStatus(context.page, 'synced');
  });

  test("should not show refresh button for new posts", async () => {
    const testTitle = "New Post No Refresh";

    // Create a new post but don't sync it
    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );
    await expectNotice(context, "Created new post");

    // Open Ghost tab
    await openGhostTab(context);

    // Wait for new post status
    await waitForGhostTabStatus(context.page, 'new-post');

    // Check that refresh button is NOT visible for new posts
    const refreshButtonExists = await context.page.locator('.ghost-sync-refresh-btn').count();
    expect(refreshButtonExists).toBe(0);
  });

  test("should handle refresh when no file is selected", async () => {
    // Open Ghost tab without any file selected
    await openGhostTab(context);

    // Check that refresh button is not visible when no file is selected
    const refreshButtonExists = await context.page.locator('.ghost-sync-refresh-btn').count();
    expect(refreshButtonExists).toBe(0);
  });
});
